@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Inter', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  @apply bg-neutral-50 dark:bg-black text-neutral-800 dark:text-neutral-100;
  margin: 0;
  padding: 0;
  transition: background-color 0.3s ease-in-out;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
body::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Hide scrollbar but maintain functionality */
.hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Modal scrollbar hide */
.modal-scroll::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.modal-scroll {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* Add tooltip styles */
.tooltip-container .tooltip {
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease-in-out;
  min-width: 200px; /* Increased width for tooltips */
  white-space: normal; /* Allow text wrapping */
  line-height: 1.4; /* Improved readability */
  z-index: 1000; /* Higher z-index to ensure tooltips appear above other content */
  border: 1px solid #e5e7eb;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dark .tooltip-container .tooltip {
  border: 1px solid #374151;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

.tooltip-container:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

/* Mobile menu transition */
.mobile-menu-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.mobile-menu-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 200ms, transform 200ms;
}

.mobile-menu-exit {
  opacity: 1;
  transform: translateY(0);
}

.mobile-menu-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 200ms, transform 200ms;
}

/* Sidebar styles removed - now using inline Tailwind classes for instant theme switching */

/* Table styling */
.data-table {
  @apply w-full border-collapse;
}

.data-table th {
  @apply text-left py-3 px-4 bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200 text-sm font-semibold first:rounded-tl-lg last:rounded-tr-lg;
}

.data-table td {
  @apply py-3 px-4 border-b border-neutral-200 dark:border-neutral-700 text-sm text-neutral-900 dark:text-neutral-100;
}

.data-table tr:last-child td {
  @apply border-0;
}

.data-table tr:hover {
  @apply bg-neutral-50 dark:bg-neutral-800;
}

/* Severity badges */
.severity-badge {
  @apply inline-flex items-center justify-center rounded-full text-xs font-medium px-2.5 py-0.5;
}

.severity-critical {
  @apply bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300;
}

.severity-high {
  @apply bg-danger-100 dark:bg-danger-900/30 text-danger-800 dark:text-danger-300;
}

.severity-medium {
  @apply bg-warning-100 dark:bg-warning-900/30 text-warning-800 dark:text-warning-300;
}

.severity-low {
  @apply bg-success-100 dark:bg-success-900/30 text-success-800 dark:text-success-300;
}

/* News animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-in-up {
  animation: slideInUp 0.5s ease-out both;
}

/* Fix pie chart click issues and remove square box */
.recharts-wrapper {
  outline: none !important;
  border: none !important;
}

.recharts-surface {
  outline: none !important;
  border: none !important;
}

.recharts-pie-sector {
  outline: none !important;
  border: none !important;
}

.recharts-pie-sector:focus {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

.recharts-pie-sector:active {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Ensure pie chart text labels stay visible */
.recharts-text {
  pointer-events: none !important;
  user-select: none !important;
  z-index: 1000 !important;
}

/* Force labels to stay visible on hover */
.recharts-pie-label-text {
  pointer-events: none !important;
  user-select: none !important;
  z-index: 1000 !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Remove any focus outlines from recharts components */
.recharts-wrapper *:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* Prevent selection highlighting */
.recharts-wrapper * {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}


@media print {
  body, html {
    background: #fff !important;
    color: #222 !important;
    font-size: 14px;
  }
  .no-print {
    display: none !important;
  }
  .dashboard-main-grid {
    display: block !important;
    width: 100% !important;
  }
  .card, .box, .panel {
    page-break-inside: avoid !important;
    box-shadow: none !important;
  }
  /* Add more adjustments as needed */
}


